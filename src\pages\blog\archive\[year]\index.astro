---
// 导入 Astro 内容集合相关的函数和类型
import { getCollection, type CollectionEntry } from 'astro:content';
// 导入基础头部组件，用于 SEO 和页面元信息
import BaseHead from '../../../../components/BaseHead.astro';
// 导入页头组件
import Header from '../../../../components/Header.astro';
// 导入页脚组件
import Footer from '../../../../components/Footer.astro';
// 导入日期格式化组件
import FormattedDate from '../../../../components/FormattedDate.astro';
// 导入归档侧边栏小部件
import ArchiveWidget from '../../../../components/ArchiveWidget.astro';
// 导入站点标题常量
import { SITE_TITLE } from '../../../../consts';

// getStaticPaths 函数用于在构建时生成此动态路由的所有可能路径 (年)
export async function getStaticPaths() {
  const posts = await getCollection('blog'); // 获取所有博客文章
  const paths = new Set<string>(); // 创建一个 Set 存储所有唯一的 "年" 组合字符串
  // 遍历文章，提取年份
  posts.forEach(post => {
    // 确保 pubDate 是有效的 Date 对象
    if (post.data.pubDate instanceof Date && !isNaN(post.data.pubDate.valueOf())) {
      const year = post.data.pubDate.getUTCFullYear().toString(); // 获取 UTC 年份
      paths.add(year); // 添加 "年" 字符串到 Set
    }
  });
  // 将 Set 转换为数组，并映射为 Astro期望的参数格式
  return Array.from(paths).map(year => {
    return { params: { year } };
  });
}

// 从 Astro.params 获取当前页面的年份参数
const { year } = Astro.params;

// 获取所有博客文章
const allPosts = await getCollection('blog');
// 根据年份参数筛选文章，并按发布日期降序排序
const postsForYear = allPosts.filter(post => {
  if (!(post.data.pubDate instanceof Date) || isNaN(post.data.pubDate.valueOf())) return false; // 再次校验日期有效性
  return post.data.pubDate.getUTCFullYear().toString() === year;
}).sort((a, b) => b.data.pubDate.valueOf() - a.data.pubDate.valueOf());

// 设置页面标题和描述 (中文)
const pageTitle = `${year}年文章归档 | ${SITE_TITLE}`;
const pageDescription = `博客中 ${year}年发布的所有文章。`;
const archiveHeading = `${year}年文章`; // 归档页主标题
const noPostsText = `在 ${year}年没有找到文章。`; // 无文章时的提示
---

{/* HTML 文档语言设置为 "zh-CN" */}
<!doctype html>
<html lang="zh-CN">
<head>
    <BaseHead title={pageTitle} description={pageDescription} />
    <style>
        /* 样式与 src/pages/blog/archive/[year]/[month].astro 基本一致 */
        /* 文章列表样式 */
        ul { display: flex; flex-wrap: wrap; gap: 2rem; list-style-type: none; margin: 0; padding: 0; }
        ul li { width: calc(50% - 1rem); } /* 默认两列 */
        ul li * { text-decoration: none; transition: 0.2s ease; }
        ul li img { margin-bottom: 0.5rem; border-radius: 12px; }
        ul li a { display: block; } /* 确保整个区域可点击 */
        .title { margin: 0; color: rgb(var(--black)); line-height: 1; } /* 标题样式 */
        .date { margin: 0; color: rgb(var(--gray)); } /* 日期样式 */
        ul li a:hover h4, ul li a:hover .date { color: rgb(var(--accent)); } /* 鼠标悬停时标题和日期颜色变化 */
        ul a:hover img { box-shadow: var(--box-shadow); } /* 鼠标悬停时图片阴影 */

        /* 页面整体布局样式 */
        .page-wrapper { display: flex; flex-direction: column; min-height: 100vh; } /* 确保页脚在底部 */
        .main-content-area { display: flex; flex-wrap: wrap; gap: 2em; width: 100%; max-width: 1280px; margin: 0 auto; padding: 1em; flex-grow: 1; } /* 主内容区域布局 */
        .blog-list-column { flex: 3; min-width: 70%; } /* 博文列表区域占据大部分空间 */
        .sidebar-column { flex: 1; min-width: 250px; } /* 侧边栏区域 */
        .archive-title { font-size: 2em; margin-bottom: 1em; color: rgb(var(--black)); } /* 归档页主标题样式 */
        
        /* 响应式布局：小屏幕 */
        @media (max-width: 960px) {
            .main-content-area { flex-direction: column; align-items: center; } /* 垂直堆叠 */
            .blog-list-column { min-width: 100%; order: 1; } /* 内容优先 */
            .sidebar-column { min-width: 100%; max-width: 720px; order: 2; } /* 侧边栏在下方 */
            ul li { width: 100%; text-align: center; } /* 列表项全宽并居中 */
        }
    </style>
</head>
<body class="page-wrapper">
    <Header /> {/* 页头组件 */}
    <main class="main-content-area">
        {/* 博文列表列 */}
        <div class="blog-list-column">
            <section>
                <h1 class="archive-title">{archiveHeading}</h1> {/* 显示归档年份标题 */}
                {/* 条件渲染：如果有该年份的文章则显示列表，否则显示提示信息 */}
                {postsForYear.length > 0 ? (
                    <ul>
                        {postsForYear.map(post => (
                            <li>
                                <a href={`/blog/${post.slug}/`}>
                                    <img width={720} height={360} src={post.data.heroImage} alt={post.data.title} />
                                    <h4 class="title">{post.data.title}</h4>
                                    <p class="date">
                                        <FormattedDate date={post.data.pubDate} /> {/* 格式化日期 */}
                                    </p>
                                </a>
                            </li>
                        ))}
                    </ul>
                ) : (
                    <p>{noPostsText}</p> {/* 无文章时的提示 */}
                )}
            </section>
        </div>
        {/* 侧边栏 */}
        <aside class="sidebar-column">
            <ArchiveWidget /> {/* 归档小部件 */}
        </aside>
    </main>
    <Footer /> {/* 页脚组件 */}
</body>
</html>