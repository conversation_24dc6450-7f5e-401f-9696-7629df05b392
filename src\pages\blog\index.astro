---
// 导入基础组件和常量
import BaseHead from '../../components/BaseHead.astro'; // 基础头部，用于SEO和元信息
import Header from '../../components/Header.astro';     // 页面头部组件
import Footer from '../../components/Footer.astro';     // 页面尾部组件
import ArchiveWidget from '../../components/ArchiveWidget.astro'; // 归档小部件
import { SITE_TITLE, SITE_DESCRIPTION } from '../../consts'; // 站点标题和描述常量
import { getCollection } from 'astro:content';        // Astro 内容集合API，用于获取博客文章
import FormattedDate from '../../components/FormattedDate.astro'; // 日期格式化组件

// 获取所有 'blog' 集合中的文章，并按发布日期降序排列
const posts = (await getCollection('blog')).sort(
	(a, b) => b.data.pubDate.valueOf() - a.data.pubDate.valueOf(),
);
---

{/* HTML 文档，语言设置为中文 */}
<!doctype html>
<html lang="zh-CN">
	<head>
		{/* 基础头部组件，传入站点标题和描述 */}
		<BaseHead title={SITE_TITLE} description={SITE_DESCRIPTION} />
		<style>
            /* 博客文章列表 (ul) 的现有样式 */
			ul {
				display: flex; /* 使用 flex 布局 */
				flex-wrap: wrap; /* 允许列表项换行 */
				gap: 2rem; /* 列表项之间的间隙 */
				list-style-type: none; /* 去除默认的列表项目符号 */
				margin: 0; /* 去除外边距 */
				padding: 0; /* 去除内边距 */
			}
			/* 列表项 (li) 样式 */
			ul li {
				width: calc(50% - 1rem); /* 在大屏幕上，每行显示两项 (考虑间隙) */
			}
			/* 列表项内所有元素的通用样式 */
			ul li * {
				text-decoration: none; /* 去除链接下划线 */
				transition: 0.2s ease; /* 平滑过渡效果 */
			}
			/* 首个列表项的特殊样式 (通常用于突出显示最新文章) */
			ul li:first-child { 
				width: 100%; /* 占满整行 */
				margin-bottom: 1rem; /* 与下方内容的间距 */
				text-align: center; /* 文本居中 */
			}
			ul li:first-child img {
				width: 100%; /* 首项图片宽度占满 */
			}
			ul li:first-child .title {
				font-size: 2.369rem; /* 首项标题字体较大 */
			}
			/* 列表项内图片样式 */
			ul li img {
				margin-bottom: 0.5rem; /* 图片与下方标题的间距 */
				border-radius: 12px; /* 图片圆角 */
			}
			/* 列表项内链接样式 */
			ul li a {
				display: block; /* 使整个列表项区域可点击 */
			}
			/* 文章标题样式 */
			.title {
				margin: 0;
				color: rgb(var(--black)); /* 标题颜色 */
				line-height: 1; /* 行高 */
			}
			/* 日期样式 */
			.date {
				margin: 0;
				color: rgb(var(--gray)); /* 日期颜色 */
			}
			/* 鼠标悬停时，列表项内标题和日期的颜色变化 */
			ul li a:hover h4,
			ul li a:hover .date {
				color: rgb(var(--accent)); /* 使用主题强调色 */
			}
			/* 鼠标悬停时，列表项内图片的阴影效果 */
			ul a:hover img {
				box-shadow: var(--box-shadow); /* 使用主题定义的阴影 */
			}
			
            /* 两栏布局的新增/调整样式 */
            /* 页面整体包裹器 */
            .page-wrapper {
                display: flex;
                flex-direction: column; /* 垂直排列页头、主内容、页脚 */
                min-height: 100vh; /* 最小高度为视口高度 */
            }
            /* 主内容区域，包含博文列表和侧边栏 */
            .main-content-area { 
                display: flex; /* flex 布局 */
                flex-wrap: wrap; /* 允许换行 */
                gap: 2em; /* 列间距 */
                width: 100%;
                max-width: 1280px; /* 主内容区最大宽度，为博客列表和侧边栏提供空间 */
                margin: 0 auto; /* 水平居中 */
                padding: 1em; /* 内边距 */
                flex-grow: 1; /* 占据可用垂直空间 */
            }
            /* 博文列表列 */
            .blog-list-column { 
                flex: 3; /* 占据较大比例空间 */
                min-width: 70%; /* 最小宽度，防止被过度挤压 */
            }
            /* 侧边栏列 */
            .sidebar-column { 
                flex: 1; /* 占据较小比例空间 */
                min-width: 250px; /* 侧边栏最小宽度 */
            }

            /* 响应式设计：针对小屏幕的媒体查询 */
			@media (max-width: 960px) { 
                .main-content-area {
                    flex-direction: column; /* 垂直堆叠 */
                    align-items: center; /* 居中对齐 */
                }
                .blog-list-column {
                    min-width: 100%; /* 博文列表占满宽度 */
                    order: 1; /* 内容优先 */
                }
                .sidebar-column {
                    min-width: 100%; /* 侧边栏占满宽度 */
                    max-width: 720px; /* 堆叠时最大宽度与内容区一致 */
                    order: 2; /* 侧边栏在内容下方 */
                }
                /* 调整列表在堆叠视图中的样式 */
				ul { 
					gap: 0.5em; /* 减小列表项间距 */
				}
				ul li {
					width: 100%; /* 列表项占满宽度 */
					text-align: center; /* 文本居中 */
				}
				ul li:first-child {
					margin-bottom: 0; /* 移除首项的下外边距 */
				}
				ul li:first-child .title {
					font-size: 1.563em; /* 调整首项标题字体大小 */
				}
			}
		</style>
	</head>
	<body class="page-wrapper"> {/* 应用页面包裹器样式 */}
		<Header /> {/* 页头 */}
		<main class="main-content-area"> {/* 主内容区域 */}
            {/* 博文列表列 */}
            <div class="blog-list-column">
                <section> {/* 包含博文列表的区域 */}
                    <ul>
                        {/* 遍历排序后的文章数据，为每篇文章创建一个列表项 */}
                        {
                            posts.map((post) => (
                                <li>
                                    {/* 链接到单篇博文页面 */}
                                    <a href={`/blog/${post.slug}/`}>
                                        {/* 文章主图片，alt 文本设为文章标题以提高可访问性 */}
                                        <img width={720} height={360} src={post.data.heroImage} alt={post.data.title} />
                                        {/* 文章标题 */}
                                        <h4 class="title">{post.data.title}</h4>
                                        {/* 文章发布日期 */}
                                        <p class="date">
                                            <FormattedDate date={post.data.pubDate} />
                                        </p>
                                    </a>
                                </li>
                            ))
                        }
                    </ul>
                </section>
            </div>
            {/* 侧边栏列 */}
            <aside class="sidebar-column">
                <ArchiveWidget /> {/* 归档小部件 */}
            </aside>
		</main>
		<Footer /> {/* 页脚 */}
	</body>
</html>
