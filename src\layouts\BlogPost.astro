---
// --- (保持 Astro frontmatter 不变) ---
// 导入 Astro 内容集合相关的类型定义
import type { CollectionEntry } from 'astro:content';
// 导入基础头部组件，用于设置页面的 <head> 内容
import BaseHead from '../components/BaseHead.astro';
// 导入页头组件
import Header from '../components/Header.astro';
// 导入页脚组件
import Footer from '../components/Footer.astro';
// 导入日期格式化组件
import FormattedDate from '../components/FormattedDate.astro';
// 导入热力图显示组件 (React)
import HeatmapDisplay from '../components/HeatmapDisplay.jsx';
// 导入评论相关组件 (React)
import CommentForm from '../components/CommentForm.jsx';
import CommentList from '../components/CommentList.jsx';
// 导入归档小部件组件
import ArchiveWidget from '../components/ArchiveWidget.astro';

// Props for BlogPost:
// - 'entry': The full CollectionEntry object for the current post.
// - All other props are from the post's frontmatter (post.data).
type Props = CollectionEntry<'blog'>['data'] & { entry: CollectionEntry<'blog'> };

const { title, description, pubDate, updatedDate, heroImage } = Astro.props; // These come from ...post.data
const entry = Astro.props.entry; // This is the full post object

// 获取所有博客文章并排序，用于“上一篇/下一篇”导航
import { getCollection } from 'astro:content';
const allPosts = await getCollection('blog');
const sortedPosts = allPosts.sort(
  (a, b) => new Date(a.data.pubDate).valueOf() - new Date(b.data.pubDate).valueOf()
);
// Log the structure of sortedPosts to verify slugs
console.log("BlogPost.astro - DEBUG: sortedPosts (first 3 entries):", JSON.stringify(sortedPosts.slice(0,3).map(p => ({id: p.id, slug: p.slug, title: p.data.title})), null, 2));


// Slug determination logic
let currentSlug = '';
let slugSource = 'unknown';

// Detailed log of the entry object passed to BlogPost
console.log("BlogPost.astro - DEBUG: Astro.props.entry object:", JSON.stringify(entry, (key, value) => typeof value === 'function' ? 'function' : value, 2));

if (entry && entry.slug && typeof entry.slug === 'string' && entry.slug.trim() !== '') {
    currentSlug = entry.slug.trim();
    slugSource = 'Astro.props.entry.slug';
} else {
    console.warn("BlogPost.astro: Astro.props.entry.slug is not a valid string or entry is missing.", {
        entryExists: !!entry,
        entrySlug: entry?.slug,
        typeofEntrySlug: typeof entry?.slug,
        title: title // Log title to help identify which page is logging
    });
    // If entry.slug fails, currentSlug remains empty, leading to no navigation buttons.
}

console.log({
    "BlogPost.astro - Slug Determination Result": true,
    source: slugSource,
    determinedSlug: currentSlug,
    entrySlugDirectAccess: entry?.slug, // Log direct access again for comparison
    title: title
});

// The postSlug for data attributes, comments, etc., should be this definitive currentSlug.
const postSlug = currentSlug;

const currentIndex = sortedPosts.findIndex(p => p.slug === currentSlug);

console.log({
    "BlogPost.astro - Index Logic Result": true,
    currentSlugForIndex: currentSlug,
    calculatedIndex: currentIndex,
    totalPostsInSortedList: sortedPosts.length,
    firstPostSlugInSortedArray: sortedPosts.length > 0 ? sortedPosts[0]?.slug : 'N/A',
    title: title
});

let prevPost = null;
let nextPost = null;

if (currentIndex !== -1 && sortedPosts.length > 0) { // 仅当当前文章被找到且文章列表不为空
  if (sortedPosts.length > 1) {
    if (currentIndex === 0) {
      // 当前是第一篇，上一篇是最后一篇
      prevPost = sortedPosts[sortedPosts.length - 1];
      nextPost = sortedPosts[currentIndex + 1];
    } else if (currentIndex === sortedPosts.length - 1) {
      // 当前是最后一篇，下一篇是第一篇
      prevPost = sortedPosts[currentIndex - 1];
      nextPost = sortedPosts[0];
    } else {
      // 中间的文章
      prevPost = sortedPosts[currentIndex - 1];
      nextPost = sortedPosts[currentIndex + 1];
    }
  } else {
    // 只有一篇文章，且被匹配到，则没有上一篇或下一篇
    prevPost = null;
    nextPost = null;
  }
} else {
  // 如果当前 slug 未找到 (currentIndex === -1) 或文章列表为空，则不显示导航
  console.warn("BlogPost.astro: Current slug not found in sorted posts or no posts available. Navigation will be hidden.", { currentSlug, currentIndex, postCount: sortedPosts.length });
  prevPost = null;
  nextPost = null;
}
---

{/* HTML 文档的 lang 属性设置为 "zh-CN" (中文) */}
<html lang="zh-CN"> 
	<head>
		{/* BaseHead 组件，用于生成页面的 SEO 相关 meta 标签和标题 */}
		<BaseHead title={title} description={description} />
		<style>
			/* --- (所有 <style> 内容保持不变，此处省略以减少重复) --- */
			.prose { width: 720px; max-width: 100%; padding: 1em; color: rgb(var(--gray-dark));}
            .page-wrapper { display: flex; flex-direction: column; min-height: 100vh; }
            .main-content-area { display: flex; flex-wrap: wrap; gap: 2em; width: 100%; max-width: 1100px; margin: 0 auto; padding: 1em; flex-grow: 1; }
            .article-column { flex: 3; min-width: 70%; }
            .sidebar-column { flex: 1; min-width: 250px; }
            @media (max-width: 960px) { 
                .main-content-area { flex-direction: column; align-items: center; }
                .article-column { min-width: 100%; order: 1; }
                .sidebar-column { min-width: 100%; max-width: 720px; order: 2; }
            }
            .comments-section { margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; }
            .heatmap-container { margin-top: 30px; }
			.hero-image { width: 100%; }
			.hero-image img { display: block; margin: 0 auto; border-radius: 12px; box-shadow: var(--box-shadow); }
			.title { margin-bottom: 1em; padding: 1em 0; text-align: center; line-height: 1; }
			.title h1 { margin: 0 0 0.5em 0; }
			.date { margin-bottom: 0.5em; color: rgb(var(--gray)); }
			.last-updated-on { font-style: italic; }

            /* 文章导航样式 */
            .post-navigation {
                margin-top: 3em;
                border-top: 1px solid var(--border-color);
                padding-top: 1.5em;
            }
            .post-navigation-list {
                list-style: none;
                padding: 0;
                margin: 0;
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap; /* 允许换行 */
            }
            .post-navigation-list li {
                flex-basis: 48%; /* 接近一半宽度，留出间隙 */
                margin-bottom: 1em; /* 移动设备上换行时的间距 */
            }
            .post-navigation-list a {
                display: block;
                padding: 0.8em 1em;
                border: 1px solid var(--border-color);
                border-radius: 8px;
                text-decoration: none;
                color: var(--text-color);
                transition: background-color 0.2s ease, border-color 0.2s ease;
            }
            .post-navigation-list a:hover {
                background-color: var(--code-bg-color);
                border-color: var(--accent);
            }
            .post-navigation-list .nav-text {
                display: block;
                font-size: 0.9em;
            }
            .post-navigation-list .arrow {
                font-size: 1.2em;
                color: var(--accent);
            }
            .prev-post a {
                text-align: left;
            }
            .next-post a {
                text-align: right;
            }
            .prev-post .arrow {
                margin-right: 0.5em;
            }
            .next-post .arrow {
                margin-left: 0.5em;
            }

            @media (max-width: 600px) {
                .post-navigation-list li {
                    flex-basis: 100%; /* 在小屏幕上，每个链接占满一行 */
                }
                .prev-post a,
                .next-post a {
                    text-align: left; /* 小屏幕上统一左对齐 */
                }
            }
		</style>
	</head>

	<body class="page-wrapper">
		<Header />
		<main class="main-content-area">
            <div class="article-column"> 
                <article>
                    <div class="hero-image">
                        {/* 在 img 的 alt 属性中也使用 title，提高可访问性 */}
                        {heroImage && <img width={1020} height={510} src={heroImage} alt={title} />}
                    </div>
                    <div class="prose"> 
                        <div class="title">
                            <div class="date">
                                <FormattedDate date={pubDate} />
                            </div>
                            { updatedDate && (<div class="last-updated-on"> 最后更新于 <FormattedDate date={updatedDate} /></div>) }
                            <h1>{title}</h1>
                            <hr />
                        </div>
                        <slot /> 
                        {/* 评论区 */}
                        <div class="comments-section">
                            <CommentList postSlug={postSlug} client:visible />
                            <CommentForm postSlug={postSlug} client:visible />
                        </div>
                        <div class="heatmap-container">
                            <HeatmapDisplay client:visible />
                        </div>
                    </div>
                     {/* 上一篇/下一篇文章导航 */}
                     <nav class="post-navigation" aria-label="文章导航">
                        <ul class="post-navigation-list">
                            {prevPost && (
                                <li class="prev-post">
                                    <a href={`/blog/${prevPost.slug}/`} rel="prev">
                                        <span class="arrow">←</span>
                                        <span class="nav-text">上一篇：{prevPost.data.title}</span>
                                    </a>
                                </li>
                            )}
                            {nextPost && (
                                <li class="next-post">
                                    <a href={`/blog/${nextPost.slug}/`} rel="next">
                                        <span class="nav-text">下一篇：{nextPost.data.title}</span>
                                        <span class="arrow">→</span>
                                    </a>
                                </li>
                            )}
                        </ul>
                    </nav>
                </article>
            </div>
            <aside class="sidebar-column"> 
                <ArchiveWidget />
            </aside>
		</main>
		<Footer />

	</body>
</html>
