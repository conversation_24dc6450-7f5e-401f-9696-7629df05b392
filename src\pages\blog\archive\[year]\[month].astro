---
// 导入 Astro 内容集合相关的函数和类型
import { getCollection, type CollectionEntry } from 'astro:content';
// 导入基础头部组件，用于 SEO 和页面元信息
import BaseHead from '../../../../components/BaseHead.astro';
// 导入页头组件
import Header from '../../../../components/Header.astro';
// 导入页脚组件
import Footer from '../../../../components/Footer.astro';
// 导入日期格式化组件
import FormattedDate from '../../../../components/FormattedDate.astro';
// 导入归档侧边栏小部件
import ArchiveWidget from '../../../../components/ArchiveWidget.astro';
// 导入站点标题常量
import { SITE_TITLE } from '../../../../consts';

// getStaticPaths 函数用于在构建时生成此动态路由的所有可能路径 (年/月)
export async function getStaticPaths() {
  const posts = await getCollection('blog'); // 获取所有博客文章
  const paths = new Set<string>(); // 创建一个 Set 存储所有唯一的 "年/月" 组合字符串
  // 遍历文章，提取年份和月份
  posts.forEach(post => {
    // 确保 pubDate 是有效的 Date 对象
    if (post.data.pubDate instanceof Date && !isNaN(post.data.pubDate.valueOf())) {
      const year = post.data.pubDate.getUTCFullYear().toString(); // 获取 UTC 年份
      const month = (post.data.pubDate.getUTCMonth() + 1).toString().padStart(2, '0'); // 获取 UTC 月份 (1-12)，并补全为两位数
      paths.add(`${year}/${month}`); // 添加 "年/月" 字符串到 Set
    }
  });
  // 将 Set 转换为数组，并映射为 Astro期望的参数格式
  return Array.from(paths).map(path => {
    const [year, month] = path.split('/'); // 分割路径获取年和月
    return { params: { year, month } };
  });
}

// 从 Astro.params 获取当前页面的年份和月份参数
const { year, month } = Astro.params;
// 将月份字符串转换为数字
const monthNumber = parseInt(month, 10);

// 获取所有博客文章
const allPosts = await getCollection('blog');
// 根据年份和月份参数筛选文章，并按发布日期降序排序
const postsForMonth = allPosts.filter(post => {
  if (!(post.data.pubDate instanceof Date) || isNaN(post.data.pubDate.valueOf())) return false; // 再次校验日期有效性
  return post.data.pubDate.getUTCFullYear().toString() === year &&
         (post.data.pubDate.getUTCMonth() + 1) === monthNumber;
}).sort((a, b) => b.data.pubDate.valueOf() - a.data.pubDate.valueOf());

// 中文月份名称数组 (与 archiveHelpers.ts 中保持一致)
const monthNames = [
    "一月", "二月", "三月", "四月", "五月", "六月",
    "七月", "八月", "九月", "十月", "十一月", "十二月"
];
// 根据 monthNumber 获取中文月份名称
const monthName = monthNames[monthNumber - 1];

// 设置页面标题和描述 (中文)
const pageTitle = `${year}年${monthName}文章归档 | ${SITE_TITLE}`;
const pageDescription = `博客中 ${year}年${monthName}发布的所有文章。`;
const archiveHeading = `${year}年${monthName}文章`; // 归档页主标题
const noPostsText = `在 ${year}年${monthName}没有找到文章。`; // 无文章时的提示
---

{/* HTML 文档语言设置为 "zh-CN" */}
<!doctype html>
<html lang="zh-CN">
<head>
    <BaseHead title={pageTitle} description={pageDescription} />
    <style>
        /* 样式与 src/pages/blog/archive/[year].astro 基本一致 */
        /* 文章列表样式 */
        ul { display: flex; flex-wrap: wrap; gap: 2rem; list-style-type: none; margin: 0; padding: 0; }
        ul li { width: calc(50% - 1rem); }
        ul li * { text-decoration: none; transition: 0.2s ease; }
        ul li img { margin-bottom: 0.5rem; border-radius: 12px; }
        ul li a { display: block; }
        .title { margin: 0; color: rgb(var(--black)); line-height: 1; }
        .date { margin: 0; color: rgb(var(--gray)); }
        ul li a:hover h4, ul li a:hover .date { color: rgb(var(--accent)); }
        ul a:hover img { box-shadow: var(--box-shadow); }

        /* 页面整体布局样式 */
        .page-wrapper { display: flex; flex-direction: column; min-height: 100vh; }
        .main-content-area { display: flex; flex-wrap: wrap; gap: 2em; width: 100%; max-width: 1280px; margin: 0 auto; padding: 1em; flex-grow: 1; }
        .blog-list-column { flex: 3; min-width: 70%; } /* 博文列表区域 */
        .sidebar-column { flex: 1; min-width: 250px; } /* 侧边栏区域 */
        .archive-title { font-size: 2em; margin-bottom: 1em; color: rgb(var(--black)); } /* 归档页主标题样式 */
        
        /* 响应式布局：小屏幕 */
        @media (max-width: 960px) {
            .main-content-area { flex-direction: column; align-items: center; } /* 垂直堆叠 */
            .blog-list-column { min-width: 100%; order: 1; } /* 内容优先 */
            .sidebar-column { min-width: 100%; max-width: 720px; order: 2; } /* 侧边栏在下方 */
            ul li { width: 100%; text-align: center; } /* 列表项全宽并居中 */
        }
    </style>
</head>
<body class="page-wrapper">
    <Header /> {/* 页头组件 */}
    <main class="main-content-area">
        {/* 博文列表列 */}
        <div class="blog-list-column">
            <section>
                <h1 class="archive-title">{archiveHeading}</h1> {/* 显示归档年份和月份标题 */}
                {/* 条件渲染：如果有该年份和月份的文章则显示列表，否则显示提示信息 */}
                {postsForMonth.length > 0 ? (
                    <ul>
                        {postsForMonth.map(post => (
                            <li>
                                <a href={`/blog/${post.slug}/`}>
                                    <img width={720} height={360} src={post.data.heroImage} alt={post.data.title} />
                                    <h4 class="title">{post.data.title}</h4>
                                    <p class="date">
                                        <FormattedDate date={post.data.pubDate} />
                                    </p>
                                </a>
                            </li>
                        ))}
                    </ul>
                ) : (
                    <p>{noPostsText}</p>
                )}
            </section>
        </div>
        {/* 侧边栏 */}
        <aside class="sidebar-column">
            <ArchiveWidget /> {/* 归档小部件 */}
        </aside>
    </main>
    <Footer /> {/* 页脚组件 */}
</body>
</html>
