---
// 用户检查页面 - 用于排查登录问题
---

<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录检查 - 博客系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .title {
            color: #333;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        .input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            box-sizing: border-box;
        }
        
        .button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 1rem;
        }
        
        .button:hover {
            background-color: #0056b3;
        }
        
        .info-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .error-section {
            background: #fff5f5;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        
        .success-section {
            background: #f0fff4;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔍 用户登录问题排查</h1>
        
        <div class="info-section">
            <h3>📋 测试登录</h3>
            <form id="login-test-form">
                <div class="form-group">
                    <label for="test-email" class="label">邮箱地址</label>
                    <input type="email" id="test-email" class="input" required placeholder="输入要测试的邮箱">
                </div>
                <div class="form-group">
                    <label for="test-password" class="label">密码</label>
                    <input type="password" id="test-password" class="input" required placeholder="输入密码">
                </div>
                <button type="submit" class="button">测试登录</button>
            </form>
        </div>
        
        <div class="info-section">
            <h3>👤 用户注册测试</h3>
            <form id="register-test-form">
                <div class="form-group">
                    <label for="reg-email" class="label">邮箱地址</label>
                    <input type="email" id="reg-email" class="input" required placeholder="输入新邮箱">
                </div>
                <div class="form-group">
                    <label for="reg-password" class="label">密码</label>
                    <input type="password" id="reg-password" class="input" required placeholder="输入密码（至少6位）" minlength="6">
                </div>
                <button type="submit" class="button">测试注册</button>
            </form>
        </div>
        
        <div class="info-section">
            <h3>🔐 其他操作</h3>
            <button class="button" onclick="checkCurrentSession()">检查当前会话</button>
            <button class="button" onclick="testPasswordReset()">测试密码重置</button>
            <button class="button" onclick="signOut()">登出</button>
        </div>
        
        <div id="results"></div>
        
        <div class="info-section">
            <h3>💡 常见问题解决方案</h3>
            <ul>
                <li><strong>Invalid login credentials</strong>：邮箱或密码错误，或用户不存在</li>
                <li><strong>Email not confirmed</strong>：需要验证邮箱，检查邮箱中的验证链接</li>
                <li><strong>User not found</strong>：用户未注册，需要先注册账户</li>
                <li><strong>Too many requests</strong>：请求过于频繁，等待几分钟后重试</li>
            </ul>
        </div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';
        
        const supabaseUrl = 'https://ztqqlysnfjtneyviqrbn.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp0cXFseXNuZmp0bmV5dmlxcmJuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzY5MzY0NzMsImV4cCI6MjA1MjUxMjQ3M30.biWEJln7a4T4_GBaKF5C0ZHurBpVXbPoZtvjBNWgSNI';
        const supabase = createClient(supabaseUrl, supabaseAnonKey);
        
        const resultsDiv = document.getElementById('results');
        
        function showResult(message, type = 'info') {
            const className = type === 'error' ? 'error-section' : 
                             type === 'success' ? 'success-section' : 'info-section';
            resultsDiv.innerHTML = `<div class="${className}"><pre>${message}</pre></div>`;
        }
        
        // 测试登录
        document.getElementById('login-test-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            
            showResult('🔄 正在测试登录...');
            
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password,
                });
                
                if (error) {
                    showResult(`❌ 登录失败:
错误类型: ${error.name || 'Unknown'}
错误消息: ${error.message}
错误代码: ${error.status || 'N/A'}

详细信息:
${JSON.stringify(error, null, 2)}`, 'error');
                } else {
                    showResult(`✅ 登录成功!
用户ID: ${data.user.id}
邮箱: ${data.user.email}
邮箱已验证: ${data.user.email_confirmed_at ? '是' : '否'}
创建时间: ${data.user.created_at}
最后登录: ${data.user.last_sign_in_at}

会话信息:
访问令牌: ${data.session.access_token ? '已获取' : '未获取'}
刷新令牌: ${data.session.refresh_token ? '已获取' : '未获取'}
过期时间: ${new Date(data.session.expires_at * 1000).toLocaleString()}`, 'success');
                }
            } catch (error) {
                showResult(`❌ 登录异常:
${error.message}

堆栈跟踪:
${error.stack}`, 'error');
            }
        });
        
        // 测试注册
        document.getElementById('register-test-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('reg-email').value;
            const password = document.getElementById('reg-password').value;
            
            showResult('🔄 正在测试注册...');
            
            try {
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password,
                });
                
                if (error) {
                    showResult(`❌ 注册失败:
错误消息: ${error.message}
错误代码: ${error.status || 'N/A'}

详细信息:
${JSON.stringify(error, null, 2)}`, 'error');
                } else {
                    showResult(`✅ 注册成功!
用户ID: ${data.user?.id || '待确认'}
邮箱: ${data.user?.email || email}
需要邮箱验证: ${data.user?.email_confirmed_at ? '否' : '是'}

${data.user?.email_confirmed_at ? '' : '⚠️ 请检查邮箱并点击验证链接完成注册'}`, 'success');
                }
            } catch (error) {
                showResult(`❌ 注册异常:
${error.message}`, 'error');
            }
        });
        
        // 检查当前会话
        window.checkCurrentSession = async function() {
            showResult('🔄 正在检查当前会话...');
            
            try {
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error) {
                    showResult(`❌ 获取会话失败:
${error.message}`, 'error');
                } else if (session) {
                    showResult(`✅ 当前已登录:
用户ID: ${session.user.id}
邮箱: ${session.user.email}
过期时间: ${new Date(session.expires_at * 1000).toLocaleString()}`, 'success');
                } else {
                    showResult('ℹ️ 当前未登录');
                }
            } catch (error) {
                showResult(`❌ 检查会话异常:
${error.message}`, 'error');
            }
        };
        
        // 测试密码重置
        window.testPasswordReset = async function() {
            const email = prompt('请输入要重置密码的邮箱:');
            if (!email) return;
            
            showResult('🔄 正在发送密码重置邮件...');
            
            try {
                const { error } = await supabase.auth.resetPasswordForEmail(email, {
                    redirectTo: `${window.location.origin}/auth/reset-password`,
                });
                
                if (error) {
                    showResult(`❌ 发送重置邮件失败:
${error.message}`, 'error');
                } else {
                    showResult('✅ 密码重置邮件已发送，请检查邮箱', 'success');
                }
            } catch (error) {
                showResult(`❌ 发送重置邮件异常:
${error.message}`, 'error');
            }
        };
        
        // 登出
        window.signOut = async function() {
            try {
                await supabase.auth.signOut();
                showResult('✅ 已登出', 'success');
            } catch (error) {
                showResult(`❌ 登出失败:
${error.message}`, 'error');
            }
        };
        
        // 页面加载时检查会话
        window.addEventListener('load', () => {
            checkCurrentSession();
        });
    </script>
</body>
</html>
