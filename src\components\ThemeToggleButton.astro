---
// src/components/ThemeToggleButton.astro
---
<button id="theme-toggle" title="Toggle theme" aria-label="Toggle theme">
  <svg class="sun-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <circle cx="12" cy="12" r="5"></circle>
    <line x1="12" y1="1" x2="12" y2="3"></line>
    <line x1="12" y1="21" x2="12" y2="23"></line>
    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
    <line x1="1" y1="12" x2="3" y2="12"></line>
    <line x1="21" y1="12" x2="23" y2="12"></line>
    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
  </svg>
  <svg class="moon-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
  </svg>
</button>

<style>
  #theme-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem; /* Restore padding for better click area */
    border-radius: 50%; /* Make it circular */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color); /* Use text color for icons */
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
    line-height: 1; /* Ensure icons are not affected by line height */
  }
  #theme-toggle:hover {
    background-color: var(--code-bg-color); /* Subtle hover effect */
  }

  .sun-icon,
  .moon-icon {
    width: 1.25em;
    height: 1.25em;
  }

  /* Ensure only one icon is visible at a time */
  .sun-icon {
    display: none; /* Hidden by default */
  }
  .moon-icon {
    display: none; /* Hidden by default */
  }

  html.dark .moon-icon {
    display: block;
  }
  html:not(.dark) .sun-icon {
    display: block;
  }
</style>

<script is:inline>
  const themeToggle = document.getElementById('theme-toggle');
  const sunIcon = themeToggle.querySelector('.sun-icon');
  const moonIcon = themeToggle.querySelector('.moon-icon');

  const applyTheme = (theme) => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
      // CSS will handle icon display based on .dark class
    } else {
      document.documentElement.classList.remove('dark');
      // CSS will handle icon display based on .dark class
    }
  };

  const toggleTheme = () => {
    const currentTheme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    localStorage.setItem('theme', newTheme);
    applyTheme(newTheme);
  };

  themeToggle.addEventListener('click', toggleTheme);

  // Apply initial theme on load
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme) {
    applyTheme(savedTheme);
  } else {
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      applyTheme('dark');
    } else {
      applyTheme('light');
    }
  }
</script>