---
// 密码重置页面
// 用户点击邮件中的重置链接后会跳转到这个页面
---

<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码 - 博客系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
        }
        
        .title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        .input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }
        
        .input:focus {
            outline: none;
            border-color: #2337ff;
        }
        
        .button {
            width: 100%;
            padding: 12px;
            background-color: #2337ff;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .button:hover {
            background-color: #1e2ecc;
        }
        
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .message {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .error {
            background-color: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }
        
        .success {
            background-color: #efe;
            color: #363;
            border: 1px solid #cfc;
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #2337ff;
            text-decoration: none;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔐 设置新密码</h1>
        
        <div id="message-container"></div>
        
        <form id="reset-form">
            <div class="form-group">
                <label for="new-password" class="label">新密码</label>
                <input 
                    type="password" 
                    id="new-password" 
                    class="input" 
                    required 
                    minlength="6"
                    placeholder="请输入新密码（至少6位）"
                />
            </div>
            
            <div class="form-group">
                <label for="confirm-password" class="label">确认新密码</label>
                <input 
                    type="password" 
                    id="confirm-password" 
                    class="input" 
                    required 
                    minlength="6"
                    placeholder="请再次输入新密码"
                />
            </div>
            
            <button type="submit" class="button" id="submit-btn">
                更新密码
            </button>
        </form>
        
        <div class="back-link">
            <a href="/login">返回登录页面</a>
        </div>
    </div>

    <script type="module">
        // 导入 Supabase 客户端
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';

        // 从环境变量获取配置（在客户端需要使用 window 对象）
        const supabaseUrl = 'https://ztqqlysnfjtneyviqrbn.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp0cXFseXNuZmp0bmV5dmlxcmJuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzY5MzY0NzMsImV4cCI6MjA1MjUxMjQ3M30.biWEJln7a4T4_GBaKF5C0ZHurBpVXbPoZtvjBNWgSNI';
        const supabase = createClient(supabaseUrl, supabaseAnonKey);

        console.log('密码重置页面：Supabase 客户端初始化完成');
        
        // DOM 元素
        const form = document.getElementById('reset-form');
        const messageContainer = document.getElementById('message-container');
        const submitBtn = document.getElementById('submit-btn');
        const newPasswordInput = document.getElementById('new-password');
        const confirmPasswordInput = document.getElementById('confirm-password');
        
        // 显示消息
        function showMessage(message, type = 'error') {
            messageContainer.innerHTML = `<div class="message ${type}">${message}</div>`;
        }
        
        // 清除消息
        function clearMessage() {
            messageContainer.innerHTML = '';
        }
        
        // 检查URL参数和会话状态
        async function checkResetToken() {
            console.log('检查重置令牌和会话状态...');

            // 检查URL中是否有重置令牌
            const urlParams = new URLSearchParams(window.location.search);
            const accessToken = urlParams.get('access_token');
            const refreshToken = urlParams.get('refresh_token');
            const type = urlParams.get('type');

            console.log('URL参数:', { accessToken: !!accessToken, refreshToken: !!refreshToken, type });

            if (type === 'recovery' && accessToken && refreshToken) {
                try {
                    // 使用令牌设置会话
                    const { data, error } = await supabase.auth.setSession({
                        access_token: accessToken,
                        refresh_token: refreshToken
                    });

                    if (error) {
                        console.error('设置会话失败:', error);
                        showMessage('重置链接无效或已过期，请重新申请密码重置。');
                        return false;
                    }

                    console.log('会话设置成功:', data);
                    return true;
                } catch (error) {
                    console.error('处理重置令牌时出错:', error);
                    showMessage('重置链接处理失败，请重新申请密码重置。');
                    return false;
                }
            } else {
                // 检查现有会话
                const { data: { session }, error } = await supabase.auth.getSession();
                console.log('当前会话状态:', { session: !!session, error });

                if (error || !session) {
                    showMessage('重置链接无效或已过期，请重新申请密码重置。');
                    return false;
                }

                return true;
            }
        }
        
        // 处理表单提交
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            clearMessage();
            
            const newPassword = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            
            // 验证密码
            if (newPassword !== confirmPassword) {
                showMessage('两次输入的密码不一致，请重新输入。');
                return;
            }
            
            if (newPassword.length < 6) {
                showMessage('密码长度至少为6位。');
                return;
            }
            
            // 禁用按钮
            submitBtn.disabled = true;
            submitBtn.textContent = '更新中...';
            
            try {
                console.log('开始更新密码...');

                // 先检查当前会话状态
                const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                console.log('更新密码前的会话状态:', { session: !!session, sessionError });

                if (sessionError || !session) {
                    showMessage('会话已过期，请重新申请密码重置。');
                    return;
                }

                // 更新密码
                const { data, error } = await supabase.auth.updateUser({
                    password: newPassword
                });

                console.log('密码更新结果:', { data, error });

                if (error) {
                    console.error('密码更新失败:', error);
                    showMessage(`密码更新失败: ${error.message}`);
                } else {
                    console.log('密码更新成功');
                    showMessage('🎉 密码更新成功！正在跳转到登录页面...', 'success');

                    // 清除当前会话，强制用户重新登录
                    await supabase.auth.signOut();

                    // 3秒后跳转到登录页面
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 3000);
                }
            } catch (error) {
                console.error('密码更新异常:', error);
                showMessage('密码更新失败，请稍后再试。');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '更新密码';
            }
        });
        
        // 页面加载时检查重置令牌
        window.addEventListener('load', async () => {
            console.log('页面加载完成，开始检查重置令牌...');
            const isValid = await checkResetToken();
            if (!isValid) {
                form.style.display = 'none';
                console.log('重置令牌无效，隐藏表单');
            } else {
                console.log('重置令牌有效，显示表单');
            }
        });
    </script>
</body>
</html>
