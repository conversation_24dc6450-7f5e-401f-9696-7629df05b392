---
// 密码重置页面
// 用户点击邮件中的重置链接后会跳转到这个页面
---

<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码 - 博客系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
        }
        
        .title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        .input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }
        
        .input:focus {
            outline: none;
            border-color: #2337ff;
        }
        
        .button {
            width: 100%;
            padding: 12px;
            background-color: #2337ff;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .button:hover {
            background-color: #1e2ecc;
        }
        
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .message {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .error {
            background-color: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }
        
        .success {
            background-color: #efe;
            color: #363;
            border: 1px solid #cfc;
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #2337ff;
            text-decoration: none;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔐 设置新密码</h1>
        
        <div id="message-container"></div>
        
        <form id="reset-form">
            <div class="form-group">
                <label for="new-password" class="label">新密码</label>
                <input 
                    type="password" 
                    id="new-password" 
                    class="input" 
                    required 
                    minlength="6"
                    placeholder="请输入新密码（至少6位）"
                />
            </div>
            
            <div class="form-group">
                <label for="confirm-password" class="label">确认新密码</label>
                <input 
                    type="password" 
                    id="confirm-password" 
                    class="input" 
                    required 
                    minlength="6"
                    placeholder="请再次输入新密码"
                />
            </div>
            
            <button type="submit" class="button" id="submit-btn">
                更新密码
            </button>
        </form>
        
        <div class="back-link">
            <a href="/login">返回登录页面</a>
        </div>
    </div>

    <script>
        // 导入 Supabase 客户端
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';
        
        // 初始化 Supabase 客户端
        const supabaseUrl = import.meta.env.PUBLIC_SUPABASE_URL;
        const supabaseAnonKey = import.meta.env.PUBLIC_SUPABASE_ANON_KEY;
        const supabase = createClient(supabaseUrl, supabaseAnonKey);
        
        // DOM 元素
        const form = document.getElementById('reset-form');
        const messageContainer = document.getElementById('message-container');
        const submitBtn = document.getElementById('submit-btn');
        const newPasswordInput = document.getElementById('new-password');
        const confirmPasswordInput = document.getElementById('confirm-password');
        
        // 显示消息
        function showMessage(message, type = 'error') {
            messageContainer.innerHTML = `<div class="message ${type}">${message}</div>`;
        }
        
        // 清除消息
        function clearMessage() {
            messageContainer.innerHTML = '';
        }
        
        // 检查是否有有效的会话
        async function checkSession() {
            const { data: { session }, error } = await supabase.auth.getSession();
            
            if (error || !session) {
                showMessage('重置链接无效或已过期，请重新申请密码重置。');
                return false;
            }
            
            return true;
        }
        
        // 处理表单提交
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            clearMessage();
            
            const newPassword = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            
            // 验证密码
            if (newPassword !== confirmPassword) {
                showMessage('两次输入的密码不一致，请重新输入。');
                return;
            }
            
            if (newPassword.length < 6) {
                showMessage('密码长度至少为6位。');
                return;
            }
            
            // 禁用按钮
            submitBtn.disabled = true;
            submitBtn.textContent = '更新中...';
            
            try {
                // 更新密码
                const { error } = await supabase.auth.updateUser({
                    password: newPassword
                });
                
                if (error) {
                    showMessage(`密码更新失败: ${error.message}`);
                } else {
                    showMessage('密码更新成功！正在跳转到登录页面...', 'success');
                    
                    // 3秒后跳转到登录页面
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 3000);
                }
            } catch (error) {
                showMessage('密码更新失败，请稍后再试。');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '更新密码';
            }
        });
        
        // 页面加载时检查会话
        window.addEventListener('load', async () => {
            const isValid = await checkSession();
            if (!isValid) {
                form.style.display = 'none';
            }
        });
    </script>
</body>
</html>
