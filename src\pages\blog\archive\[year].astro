---
// 导入 Astro 内容集合相关的函数和类型
import { getCollection, type CollectionEntry } from 'astro:content';
// 导入基础头部组件，用于 SEO 和页面元信息
import BaseHead from '../../../components/BaseHead.astro';
// 导入页头组件
import Header from '../../../components/Header.astro';
// 导入页脚组件
import Footer from '../../../components/Footer.astro';
// 导入日期格式化组件
import FormattedDate from '../../../components/FormattedDate.astro';
// 导入归档侧边栏小部件
import ArchiveWidget from '../../../components/ArchiveWidget.astro';
// 导入站点标题常量
import { SITE_TITLE } from '../../../consts';

// getStaticPaths 函数用于在构建时生成此动态路由的所有可能路径
export async function getStaticPaths() {
  const posts = await getCollection('blog'); // 获取所有博客文章
  const years = new Set<string>(); // 创建一个 Set 存储所有唯一的年份
  // 遍历文章，提取年份
  posts.forEach(post => {
    // 确保 pubDate 是有效的 Date 对象
    if (post.data.pubDate instanceof Date && !isNaN(post.data.pubDate.valueOf())) {
      years.add(post.data.pubDate.getUTCFullYear().toString()); // 添加年份到 Set
    }
  });
  // 将 Set 转换为数组，并映射为 Astro期望的参数格式
  return Array.from(years).map(year => ({ params: { year } }));
}

// 从 Astro.params 获取当前页面的年份参数
const { year } = Astro.params;

// 获取所有博客文章
const allPosts = await getCollection('blog');
// 根据年份参数筛选文章，并按发布日期降序排序
const postsForYear = allPosts.filter(post => {
  if (!(post.data.pubDate instanceof Date) || isNaN(post.data.pubDate.valueOf())) return false; // 再次校验日期有效性
  return post.data.pubDate.getUTCFullYear().toString() === year;
}).sort((a, b) => b.data.pubDate.valueOf() - a.data.pubDate.valueOf());

// 设置页面标题和描述 (中文)
const pageTitle = `${year}年文章归档 | ${SITE_TITLE}`;
const pageDescription = `博客中 ${year} 年发布的所有文章。`;
const archiveHeading = `${year}年文章`; // 归档页主标题
const noPostsText = `在 ${year} 年没有找到文章。`; // 无文章时的提示
---

{/* HTML 文档语言设置为 "zh-CN" */}
<!doctype html>
<html lang="zh-CN">
<head>
    <BaseHead title={pageTitle} description={pageDescription} />
    <style>
        /* 样式与 src/pages/blog/index.astro 和 src/layouts/BlogPost.astro 中的两栏布局样式保持一致 */
        /* 文章列表样式 */
        ul {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem; /* 列表项之间的间隙 */
            list-style-type: none; /* 去除列表项目符号 */
            margin: 0;
            padding: 0;
        }
        ul li {
            width: calc(50% - 1rem); /* 两列布局，每项占约一半宽度 */
        }
        ul li * {
            text-decoration: none; /* 去除链接下划线 */
            transition: 0.2s ease; /* 平滑过渡效果 */
        }
        ul li img {
            margin-bottom: 0.5rem; /* 图片下方间距 */
            border-radius: 12px; /* 图片圆角 */
        }
        ul li a {
            display: block; /* 使整个卡片可点击 */
        }
        .title { /* 文章标题样式 */
            margin: 0;
            color: rgb(var(--black));
            line-height: 1;
        }
        .date { /* 日期样式 */
            margin: 0;
            color: rgb(var(--gray));
        }
        ul li a:hover h4, /* 鼠标悬停时标题和日期颜色变化 */
        ul li a:hover .date {
            color: rgb(var(--accent));
        }
        ul a:hover img { /* 鼠标悬停时图片阴影效果 */
            box-shadow: var(--box-shadow);
        }

        /* 页面整体布局样式 */
        .page-wrapper { display: flex; flex-direction: column; min-height: 100vh; }
        .main-content-area { display: flex; flex-wrap: wrap; gap: 2em; width: 100%; max-width: 1280px; margin: 0 auto; padding: 1em; flex-grow: 1; }
        .blog-list-column { flex: 3; min-width: 70%; } /* 博文列表区域 */
        .sidebar-column { flex: 1; min-width: 250px; } /* 侧边栏区域 */
        .archive-title { font-size: 2em; margin-bottom: 1em; color: rgb(var(--black)); } /* 归档页主标题样式 */

        /* 响应式布局：小屏幕 */
        @media (max-width: 960px) {
            .main-content-area { flex-direction: column; align-items: center; } /* 垂直堆叠 */
            .blog-list-column { min-width: 100%; order: 1; } /* 内容优先 */
            .sidebar-column { min-width: 100%; max-width: 720px; order: 2; } /* 侧边栏在下方 */
            ul li { width: 100%; text-align: center; } /* 列表项全宽并居中 */
        }
    </style>
</head>
<body class="page-wrapper">
    <Header /> {/* 页头组件 */}
    <main class="main-content-area">
        {/* 博文列表列 */}
        <div class="blog-list-column">
            <section>
                <h1 class="archive-title">{archiveHeading}</h1> {/* 显示归档年份标题 */}
                {/* 条件渲染：如果有该年份的文章则显示列表，否则显示提示信息 */}
                {postsForYear.length > 0 ? (
                    <ul>
                        {postsForYear.map(post => (
                            <li>
                                <a href={`/blog/${post.slug}/`}> {/* 链接到单篇博文 */}
                                    <img width={720} height={360} src={post.data.heroImage} alt={post.data.title} /> {/* 文章图片，alt属性使用文章标题 */}
                                    <h4 class="title">{post.data.title}</h4> {/* 文章标题 */}
                                    <p class="date">
                                        <FormattedDate date={post.data.pubDate} /> {/* 格式化日期 */}
                                    </p>
                                </a>
                            </li>
                        ))}
                    </ul>
                ) : (
                    <p>{noPostsText}</p> {/* 无文章时的提示 */}
                )}
            </section>
        </div>
        {/* 侧边栏 */}
        <aside class="sidebar-column">
            <ArchiveWidget /> {/* 归档小部件 */}
        </aside>
    </main>
    <Footer /> {/* 页脚组件 */}
</body>
</html>
