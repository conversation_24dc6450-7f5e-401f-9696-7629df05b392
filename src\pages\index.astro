---
import { getCollection } from 'astro:content';
import BaseHead from '../components/BaseHead.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import FormattedDate from '../components/FormattedDate.astro';
import { SITE_TITLE, SITE_DESCRIPTION } from '../consts';

const posts = (await getCollection('blog')).sort(
	(a, b) => b.data.pubDate.valueOf() - a.data.pubDate.valueOf()
);
---

<!doctype html>
<html lang="en">
	<head>
		<BaseHead title={SITE_TITLE} description={SITE_DESCRIPTION} />
	</head>
	<body>
		<Header />
		<main>
			<section>
				<ul>
					{
						posts.map((post) => (
							<li>
								<a href={`/blog/${post.slug}/`}>
									<h4 class="title">{post.data.title}</h4>
								</a>
								<FormattedDate date={post.data.pubDate} />
								{post.data.heroImage && <img width={1020} height={510} src={post.data.heroImage} alt={post.data.title} />}
								<p>{post.data.description}</p>
							</li>
						))
					}
				</ul>
			</section>
		</main>
		<Footer />
	</body>
</html>
