/*
  The CSS in this style tag is based off of Bear Blog's default CSS.
  https://github.com/HermanMartinus/bearblog/blob/297026a877bc2ab2b3bdfbd6b9f7961c350917dd/templates/styles/blog/default.css
  License MIT: https://github.com/<PERSON>M<PERSON>inus/bearblog/blob/master/LICENSE.md
 */

:root {
	/* Light mode (default) */
	--accent: #2337ff;
	--accent-dark: #000d8a;
	--black: 15, 18, 25; /* Used as rgb(var(--black)) */
	--gray: 96, 115, 159; /* Used as rgb(var(--gray)) */
	--gray-light: 229, 233, 240; /* Used as rgb(var(--gray-light)) */
	--gray-dark: 34, 41, 57; /* Used as rgb(var(--gray-dark)) */
	
	--box-shadow:
		0 2px 6px rgba(var(--gray), 25%), 0 8px 24px rgba(var(--gray), 33%),
		0 16px 32px rgba(var(--gray), 33%);

	/* Theme specific variables */
	--bg-color: #ffffff; /* White background for light mode */
	--text-color: rgb(var(--gray-dark)); /* Dark gray text for light mode */
	--heading-color: rgb(var(--black)); /* Black headings for light mode */
	--link-color: var(--accent);
	--link-hover-color: var(--accent-dark);
	--code-bg-color: rgb(var(--gray-light));
	--border-color: rgb(var(--gray-light));
	--body-gradient: linear-gradient(rgba(var(--gray-light), 0.5), #fff) no-repeat; /* Adjusted light mode gradient */
}

html.dark {
	/* Dark mode overrides when .dark class is applied to html element */
	--accent: #5e81ac; /* Example: Nord blue for dark mode */
	--accent-dark: #81a1c1;
	--black: 236, 239, 244; /* Lighter black for dark mode text if needed, or use text-color directly */
	--gray: 163, 174, 194; /* Lighter gray for dark mode */
	--gray-light: 76, 86, 106; /* Darker gray-light for dark mode (e.g., borders) */
	--gray-dark: 216, 222, 233; /* Lighter gray-dark for dark mode text */

	--bg-color: #2e3440; /* Dark background for dark mode (Nord polar night) */
	--text-color: #d8dee9; /* Light gray text for dark mode (Nord snow storm) */
	--heading-color: #eceff4; /* Lighter headings for dark mode */
	--link-color: var(--accent);
	--link-hover-color: var(--accent-dark);
	--code-bg-color: #3b4252; /* Darker code background */
	--border-color: #4c566a; /* Darker border */
	--body-gradient: linear-gradient(rgba(46, 52, 64, 0.8), #2e3440) no-repeat; /* Darker gradient for dark mode */
}
@font-face {
	font-family: 'Atkinson';
	src: url('/fonts/atkinson-regular.woff') format('woff');
	font-weight: 400;
	font-style: normal;
	font-display: swap;
}
@font-face {
	font-family: 'Atkinson';
	src: url('/fonts/atkinson-bold.woff') format('woff');
	font-weight: 700;
	font-style: normal;
	font-display: swap;
}
body {
	font-family: 'Atkinson', sans-serif;
	margin: 0;
	padding: 0;
	text-align: left;
	background: var(--body-gradient); /* Use variable */
	background-color: var(--bg-color); /* Ensure background color is set */
	background-size: 100% 600px;
	word-wrap: break-word;
	overflow-wrap: break-word;
	color: var(--text-color); /* Use variable */
	font-size: 20px;
	line-height: 1.7;
}
main {
	width: 720px;
	max-width: calc(100% - 2em);
	margin: auto;
	padding: 3em 1em;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 0 0 0.5rem 0;
	color: var(--heading-color); /* Use variable */
	line-height: 1.2;
}
h1 {
	font-size: 3.052em;
}
h2 {
	font-size: 2.441em;
}
h3 {
	font-size: 1.953em;
}
h4 {
	font-size: 1.563em;
}
h5 {
	font-size: 1.25em;
}
strong,
b {
	font-weight: 700;
}
a {
	color: var(--link-color); /* Use variable */
}
a:hover {
	color: var(--link-hover-color); /* Use variable */
}
p {
	margin-bottom: 1em;
}
.prose p {
	margin-bottom: 2em;
}
textarea {
	width: 100%;
	font-size: 16px;
}
input {
	font-size: 16px;
}
table {
	width: 100%;
}
img {
	max-width: 100%;
	height: auto;
	border-radius: 8px;
}
code {
	padding: 2px 5px;
	background-color: var(--code-bg-color); /* Use variable */
	color: var(--text-color); /* Ensure code text also adapts */
	border-radius: 2px;
}
pre {
	padding: 1.5em;
	border-radius: 8px;
	background-color: var(--code-bg-color); /* Use variable for pre background */
	color: var(--text-color); /* Ensure pre text also adapts */
}
pre > code {
	all: unset;
    /* The color will be inherited from 'pre' or can be set explicitly if needed */
}
blockquote {
	border-left: 4px solid var(--accent); /* Accent color is already a variable, will adapt if accent changes in dark mode */
	padding: 0 0 0 20px;
	margin: 0px;
	font-size: 1.333em;
	color: var(--text-color); /* Ensure blockquote text also adapts */
}
hr {
	border: none;
	border-top: 1px solid var(--border-color); /* Use variable */
}
@media (max-width: 720px) {
	body {
		font-size: 18px;
	}
	main {
		padding: 1em;
	}
}

.sr-only {
	border: 0;
	padding: 0;
	margin: 0;
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	/* IE6, IE7 - a 0 height clip, off to the bottom right of the visible 1px box */
	clip: rect(1px 1px 1px 1px);
	/* maybe deprecated but we need to support legacy browsers */
	clip: rect(1px, 1px, 1px, 1px);
	/* modern browsers, clip-path works inwards from each corner */
	clip-path: inset(50%);
	/* added line to stop words getting smushed together (as they go onto separate lines and some screen readers do not understand line feeds as a space */
	white-space: nowrap;
}
