---
// 导入 HeaderLink 子组件，用于导航链接
import HeaderLink from './HeaderLink.astro';
// 导入站点标题常量
import { SITE_TITLE } from '../consts';
// 导入主题切换按钮组件
import ThemeToggleButton from './ThemeToggleButton.astro';

// 定义内部导航链接的文本和路径 (中文)
const internalLinks = [
    { href: "/blog", text: "博客" },
    { href: "/about", text: "关于" },
    { href: "/rss.xml", text: "RSS" }
];

// 定义“退出登录”按钮的文本
const logoutButtonText = "退出登录";
// 定义“登录”和“注册”链接的文本
const loginLinkText = "登录";
const registerLinkText = "注册";
const loadingUserText = "加载中..."; // 用户信息加载中状态 (直接使用默认值)

---

{/* 页面头部容器 */}
<header>
	{/* 导航栏 */}
	<nav>
		{/* 站点标题/Logo，链接到首页 */}
		<h2><a href="/">{SITE_TITLE}</a></h2> {/* 使用从 consts.ts 导入的站点标题 */}
		
		{/* 内部页面导航链接区域 */}
		<div class="internal-links">
            {internalLinks.map(link => (
                <HeaderLink href={link.href}>{link.text}</HeaderLink>
            ))}
            {/* 登录和注册链接，初始显示，通过脚本根据认证状态控制 */}
            <HeaderLink href="/login" id="login-link" class="auth-link">{loginLinkText}</HeaderLink>
            <HeaderLink href="/register" id="register-link" class="auth-link">{registerLinkText}</HeaderLink>
            <ThemeToggleButton /> {/* 在此处添加主题切换按钮 */}
		</div>

        {/* 用户相关操作区域 - 初始隐藏或显示加载状态，通过脚本控制 */}
        <div id="user-session-controls" style="display: none; align-items: center; gap: 1em;">
            <span id="user-identifier" style="font-size: 0.9em;">{loadingUserText}</span>
            <button type="button" id="logout-button" class="logout-button">{logoutButtonText}</button>
        </div>

		{/* 社交媒体链接区域已被移除 */}
	</nav>
</header>

<script>
    // 客户端脚本，用于处理用户认证状态相关的 UI 更新和登出操作
    import { supabase } from '@/lib/supabaseClient'; // 导入 Supabase 客户端
    import { authStore } from '@/stores/authStore';    // 导入全局认证状态存储
    import { initializeAuthListener } from '@/lib/authHandler'; // 导入认证状态监听器初始化函数

    initializeAuthListener(); // 初始化 Supabase 认证状态监听器

    const userSessionControls = document.getElementById('user-session-controls');
    const userIdentifierDisplay = document.getElementById('user-identifier');
    const logoutButton = document.getElementById('logout-button');
    const loginLink = document.getElementById('login-link');
    const registerLink = document.getElementById('register-link');
    // loadingUserText 从 frontmatter 传递下来，并在 userIdentifierDisplay 初始化时使用
    // currentLoadingUserText 用于在脚本中动态设置加载文本，确保一致性
    const currentLoadingUserText = "{loadingUserText}"; // Astro 会将 frontmatter 变量注入

    const updateAuthUI = (authState) => {
        // 新增日志: 记录传入的 authState
        console.log("Header.astro - updateAuthUI: 接收到 authState:", authState);

        if (!userSessionControls || !userIdentifierDisplay || !loginLink || !registerLink) {
            console.warn("Header Auth UI: 部分DOM元素未找到，UI可能不会正确更新。");
            return;
        }

        if (authState.isLoading) {
            // 新增日志: 指明当前应用的 UI 状态
            console.log("Header.astro - updateAuthUI: 正在应用“加载中”的UI状态。");
            userIdentifierDisplay.textContent = currentLoadingUserText; // 使用脚本中定义的加载文本
            userSessionControls.style.display = 'flex'; 
            loginLink.style.display = 'none';
            registerLink.style.display = 'none';
            if(logoutButton) logoutButton.style.display = 'none'; 
        } else if (authState.user) {
            // 新增日志: 指明当前应用的 UI 状态
            console.log("Header.astro - updateAuthUI: 正在应用“用户已登录”的UI状态。用户:", authState.user.email);
            userSessionControls.style.display = 'flex'; 
            userIdentifierDisplay.textContent = authState.user.email || '用户'; // 显示用户邮箱或通用文本 "用户"
            if(logoutButton) logoutButton.style.display = 'inline-block'; 
            loginLink.style.display = 'none'; 
            registerLink.style.display = 'none'; 
        } else {
            // 新增日志: 指明当前应用的 UI 状态
            console.log("Header.astro - updateAuthUI: 正在应用“用户未登录”的UI状态。");
            userSessionControls.style.display = 'none'; 
            userIdentifierDisplay.textContent = ''; 
            loginLink.style.display = 'inline'; 
            registerLink.style.display = 'inline'; 
        }
    };

    const unsubscribe = authStore.subscribe(state => { 
        // 更新日志信息，使其更明确
        console.log('Header.astro - authStore.subscribe: authStore 状态已改变，新状态:', state);
        updateAuthUI(state);
    });

    const handleLogout = async () => { 
        if(logoutButton) logoutButton.disabled = true;
        console.log("Header.astro - handleLogout: 用户点击登出按钮。"); // 新增日志
        const { error } = await supabase.auth.signOut();
        if (error) {
            console.error('Header.astro - handleLogout: 登出时发生错误:', error.message); // 更新日志信息
            alert('登出失败: ' + error.message); 
            if(logoutButton) logoutButton.disabled = false;
        } else {
            console.log('Header.astro - handleLogout: 用户已成功从 Supabase 登出。UI 将通过 authStore 更新。'); // 更新日志信息
        }
    };

    if (logoutButton) { // 添加登出按钮的事件监听器
        logoutButton.addEventListener('click', handleLogout);
    }
</script>

<style>
	/* 头部整体样式 */
	header { margin: 0; padding: 0 1em; background: white; box-shadow: 0 2px 8px rgba(var(--black), 5%); }
	/* 导航栏样式 */
	nav { display: flex; align-items: center; justify-content: space-between; }
	/* 站点标题 (h2) 样式 */
	h2 { margin: 0; font-size: 1em; }
	/* 站点标题链接样式 */
	h2 a, h2 a.active { text-decoration: none; font-weight: 700; color: rgb(var(--black)); }
	/* 内部链接容器的 flex 布局 */
	.internal-links { display: flex; align-items: center; gap: 1em; }
    /* .social-links 相关的样式可以保留或移除，如果确认不再使用 .social-links 类名 */
	/* .social-links a { display: block; padding: 0.5em; color: rgb(var(--gray)); text-decoration: none; } */
	/* .social-links a:hover { color: rgb(var(--gray-dark)); } */

    /* 登出按钮样式 */
    .logout-button {
        background-color: var(--accent); color: white; border: none;
        padding: 0.4em 0.8em; border-radius: 4px; cursor: pointer;
        font-size: 0.9em; transition: background-color 0.2s ease;
    }
    .logout-button:hover { background-color: var(--accent-dark); }
    .logout-button:disabled { background-color: var(--gray); cursor: not-allowed; }
    /* 登录/注册链接的样式 */
    .auth-link { margin-left: 0.5em; }
</style>
